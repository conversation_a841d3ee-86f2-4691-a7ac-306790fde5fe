<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="ecommerce_order_view_kanban" model="ir.ui.view">
        <field name="name">ecommerce.order.view.kanban</field>
        <field name="model">ecommerce.order</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" edit="false"
                create="false" group_create="false" delete="false"
                default_group_by="utm_medium_id" default_order="created_at desc"
                group_delete="false" group_edit="false" records_draggable="0">
                <field name="utm_medium_id" />
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <widget name="web_ribbon" text="DRAFT" bg_color="bg-info"
                                invisible="state != 'draft'" />
                            <widget name="web_ribbon" text="WAITING" bg_color="bg-warning"
                                invisible="state != 'waiting'" />
                            <widget name="web_ribbon" text="DONE" bg_color="bg-success"
                                invisible="state != 'done'" />
                            <widget name="web_ribbon" text="CANCELLED" bg_color="bg-danger"
                                invisible="state != 'cancelled'" />
                            <div class="row mb4">
                                <div class="col-6">
                                    <strong>
                                        <field name="order_code" invisible="order_type == 'return'"
                                            class="text-success" />
                                        <field name="return_order_code"
                                            invisible="order_type != 'return'"
                                            class="text-danger" />
                                    </strong>
                                </div>
                            </div>
                            <div class="col mt-1">
                                <span>
                                    <field name="utm_medium_name" widget="badge"
                                    decoration-info="utm_medium_name == 'SHOPEE'"
                                    decoration-danger="utm_medium_name == 'LAZADA'"
                                    decoration-warning="utm_medium_name == 'TIKI'"
                                    decoration-success="utm_medium_name == 'TIKTOK'"
                                    class="ecommerce_channel_badges me-2" />
                                </span>
                                <span>
                                    <field name="checkout_shipping_carrier"
                                    decoration-danger="checkout_shipping_carrier == 'Hoả tốc'"
                                    widget="badge" />
                                </span>
                                <span class="ms-2">
                                    <field name="order_status" invisible="1" />
                                    <field name="order_status_vn" widget="badge"
                                        decoration-success="order_status== 'COMPLETED'"
                                        decoration-danger="order_status == 'CANCELLED'"
                                        decoration-warning="order_status in ['PROCESSED', 'READY_TO_SHIP', 'SHIPPED', 'TO_CONFIRM_RECEIVE']" />
                                </span>
                            </div>
                            <div class="row mt-1">
                                <div class="col-5 text-muted">
                                    <span>
                                        Tracking Code
                                    </span>
                                </div>
                                <div class="col-7 text-end">
                                    <strong class="float-right">
                                        <field name="tracking_code" />
                                    </strong>
                                </div>
                            </div>
                            <div class="row mt2">
                                <div class="col-4">
                                    <strong>
                                        <field name="currency_id" invisible="1" />
                                        <field name="total_amount" widget="monetary"
                                            options="{'currency_field': 'currency_id'}" />
                                    </strong>
                                </div>
                                <div class="col-8 text-end">
                                    <strong>
                                        <field name="created_at" />
                                    </strong>
                                    <!-- <span class="float-right">
                                        <field name="state" widget="badge"
                                            decoration-success="state== 'done'"
                                            decoration-danger="state == 'cancelled'"
                                            decoration-info="state == 'draft'"
                                            decoration-warning="state == 'waiting'" />
                                    </span> -->
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- View ecommerce.order View List -->
    <record id="view_ecommerce_order_list" model="ir.ui.view">
        <field name="name">view.ecommerce.order.list</field>
        <field name="model">ecommerce.order</field>
        <field name="arch" type="xml">
            <list decoration-success=" state== 'done'"
                decoration-danger="state == 'cancelled'"
                decoration-info="state == 'draft'"
                decoration-warning="state == 'waiting'"
                create="0"
                edit="0"
                delete="0"
                limit="80"
                default_order="create_date desc"
            >
                <field name="ecommerce_shop_id" optional="show" />
                <field name="ec_warehouse_id" optional="show" />
                <field name="order_code" />
                <field name="return_order_code" optional="hide" />
                <field name="tracking_code" />
                <field name="is_cod" widget='boolean_toggle' readonly="1" />
                <field name="shipping_carrier" readonly="1" />
                <field name="checkout_shipping_carrier" widget="badge" decoration-danger="checkout_shipping_carrier == 'Hoả tốc'" readonly="1" />
                <field name="utm_medium_name" decoration-info="utm_medium_name == 'SHOPEE'"
                    decoration-danger="utm_medium_name == 'LAZADA'"
                    decoration-warning="utm_medium_name == 'TIKI'"
                    decoration-success="utm_medium_name == 'TIKTOK'"
                    class="ecommerce_channel_badges"
                    widget="badge" />
                <field name="order_status" column_invisible="1" />
                <field name="order_status_vn" widget="badge"
                    decoration-success="order_status== 'COMPLETED'"
                    decoration-danger="order_status == 'CANCELLED'"
                    decoration-warning="order_status in ['PROCESSED', 'READY_TO_SHIP', 'SHIPPED', 'TO_CONFIRM_RECEIVE']" />
                <field name="order_type" widget="badge" decoration-success=" order_type== 'normal'"
                    decoration-danger="order_type == 'cancel'"
                    decoration-info="order_type == 'return'"
                    decoration-warning="order_type == 'refund'" />
                <field name="state" widget="badge" decoration-success=" state== 'done'"
                    decoration-danger="state == 'cancelled'"
                    decoration-info="state == 'draft'"
                    decoration-warning="state == 'waiting'" />
                <!-- <field name="payment_method" /> -->
                <field name="total_amount" sum="Total" />
                <field name="note" optional="show" />
                <field name="company_id" column_invisible="True" />
                <field name="currency_id" column_invisible="True" />
                <field name="created_at" optional="hide" />
                <field name="updated_at" optional="hide" />
                <field name="create_date" optional="hide" />
            </list>
        </field>
    </record>

    <!-- View ecommerce.order form -->
    <record id="view_ecommerce_order_form" model="ir.ui.view">
        <field name="name">view.ecommerce.order.form</field>
        <field name="model">ecommerce.order</field>
        <field name="arch" type="xml">
            <form string="Ecommerce Order" create="0">
                <header>
                    <field name="state" widget="statusbar" nolabel="1"
                        options="{'clickable': False}" statusbar_visible="draft,waiting,done"
                        invisible="state == 'cancelled'" />
                    <field name="state" widget="statusbar" nolabel="1"
                        options="{'clickable': False}"
                        statusbar_visible="draft,waiting,done,cancelled"
                        invisible="state != 'cancelled'" />
                    <button string="Create Sale Order" name="action_create_sale_order"
                        type="object" class="oe_highlight"
                        invisible="sale_order_id != False or order_type == 'return' or order_status in ['CANCELLED',
                    'COMPLETED', 'SHIPPED', 'TO_CONFIRM_RECEIVE']"
                    />
                    <!-- <button string="Create Sale Order" name="action_create_sale_order"
                        type="object" class="oe_highlight"
                    /> -->
                    <button string="Create Return Order" name="action_create_sale_order_return"
                        type="object" class="oe_highlight"
                        invisible="sale_order_id != False or order_type != 'return' or order_status in ['CANCELLED', 'COMPLETED', 'SHIPPED']" />
                    <button string="Open Sale Order" name="action_view_sale_order"
                        type="object" class="oe_outline" invisible="sale_order_id == False" />
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_sale_order" type="object" class="oe_stat_button"
                            icon="fa-pencil-square-o" invisible="sale_order_count == 0">
                            <field name="sale_order_count" widget="statinfo" string="Sale Orders" />
                        </button>
                    </div>
                    <widget name="web_ribbon" text="SHOPEE" bg_color="bg-shopee"
                        invisible="utm_medium_name != 'SHOPEE'" />
                    <widget name="web_ribbon" text="LAZADA" bg_color="bg-lazada"
                        invisible="utm_medium_name != 'LAZADA'" />
                    <widget name="web_ribbon" text="TIKI" bg_color="bg-tiki"
                        invisible="utm_medium_name != 'TIKI'" />
                    <widget name="web_ribbon" text="TIKTOK" bg_color="bg-tiktok"
                        invisible="utm_medium_name != 'TIKTOK'" />
                    <div class="oe_title">
                        <label for="order_code" invisible="order_type == 'return'" />
                        <label for="return_order_code" invisible="order_type != 'return'" />
                        <h1>
                            <field name="order_code" placeholder="Name..." readonly="1"
                                invisible="order_type == 'return'" />
                            <field name="return_order_code" placeholder="Name..." readonly="1"
                                invisible="order_type != 'return'" />
                        </h1>
                    </div>
                    <group name="basic_info">
                        <group name="order_info" string="Order Information">
                            <field name="ecommerce_shop_id" readonly="1" />
                            <field name="ec_warehouse_id" readonly="1" />
                            <field name="order_status" widget="badge" readonly="1"
                                decoration-success="order_status== 'COMPLETED'"
                                decoration-danger="order_status == 'CANCELLED'"
                                decoration-warning="order_status == 'PROCESSED'" />
                            <!-- <field name="sale_order_ids" widget="many2many" readonly="1" /> -->
                            <field name="warehouse_code" readonly="1" />
                            <field name="warehouse_name" readonly="1" />
                            <field name="warehouse_id" readonly="locked" />
                            <field name="total_amount" readonly="1" />
                            <field name="created_at" readonly="1" />
                            <field name="updated_at" readonly="1" />
                            <field name="order_type" readonly="1" widget="badge"
                                decoration-success=" order_type== 'normal'"
                                decoration-danger="order_type == 'cancel'"
                                decoration-info="order_type == 'return'"
                                decoration-warning="order_type == 'refund'" />
                            <field name="company_id" invisible="1" />
                            <field name="currency_id" invisible="1" />
                        </group>
                        <group name="customer_info" string="Customer Information">
                            <field name="customer_name" readonly="1" />
                            <field name="res_partner_id" readonly="locked" />
                            <field name="res_partner_invoice_id" readonly="locked" />
                            <field name="res_partner_shipping_id" readonly="locked" />
                            <!-- <field name="created_at" /> -->
                        </group>
                    </group>
                    <br />
                    <group name="shipping_info">
                        <group name="shipping" string="Shipping">
                            <field name="shipping_address_full" readonly="1" />
                            <field name="shipping_city" readonly="1" />
                            <field name="status_histories" readonly="1" />
                        </group>
                        <group name="delivery" string="Delivery">
                            <field name="shipping_carrier" readonly="1" />
                            <field name="tracking_code" readonly="1" />
                            <field name="shipping_fee" readonly="1" />
                            <field name="shipping_fee_discount" readonly="1" />
                        </group>
                    </group>
                    <br />
                    <group name="return_order_info" string="Return Order"
                        invisible="order_type != 'return'">
                        <field name="reason" readonly="1" />
                        <field name="text_reason" readonly="1" />
                        <field name="order_code" readonly="1" />
                        <field name="refund_status" readonly="1" widget="badge"
                            decoration-success=" refund_status== 'ACCEPTED'" />
                        <field name="images" readonly="1" />
                        <field name="videos" readonly="1" />
                    </group>
                    <br />
                    <group>
                        <field name="raw_data_json" readonly="1" />
                    </group>
                    <notebook>
                        <page name="order_lines" string="Order lines">
                            <field name="order_line_ids" widget="sol_o2m" mode="list,kanban"
                                readonly="1">
                                <kanban>
                                    <field name="product_name" />
                                    <field name="model_name" />
                                    <field name="quantity" />
                                    <field name="price" />
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div t-attf-class="oe_kanban_global_click">
                                                <div class="row">
                                                    <span>Product Name: <strong>
                                                            <field
                                                                name="product_name" />
                                                        </strong></span>
                                                    <span class="mt-1">Model Name: <strong>
                                                            <field
                                                                name="model_name" />
                                                        </strong></span>
                                                </div>
                                                <div class="row">
                                                    <span class="mt-1">Quantity: <strong>
                                                            <field name="quantity" />
                                                        </strong></span>
                                                    <span class="mt-1">Price: <strong>
                                                            <field name="currency_id" invisible="1" />
                                                            <field name="price"
                                                                widget="monetary"
                                                                options="{'currency_field': 'currency_id'}" />
                                                        </strong></span>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                                <list>
                                    <field name="order_item_id" readonly="1" optional="hide" />
                                    <field name="product_name" readonly="1" />
                                    <field name="model_name" readonly="1" />
                                    <field name="wh_partner_code" readonly="1" optional="show" />
                                    <field name="item_id" readonly="1" optional="hide" />
                                    <field name="model_id" readonly="1" optional="hide" />
                                    <field name="sku" readonly="1" optional="hide" />
                                    <field name="quantity" readonly="1" />
                                    <field name="price" readonly="1" />
                                    <field name="original_price" readonly="1" optional="hide" />
                                    <field name="ref_virtual_id" readonly="1" optional="hide" />
                                    <field name="company_id" column_invisible="True" />
                                    <field name="currency_id" column_invisible="True" />
                                </list>
                            </field>
                        </page>
                        <page name="package_list" string="Package List">
                            <field name="package_list_ids" readonly="1">
                                <list>
                                    <field name="package_number" readonly="1" />
                                    <field name="tracking_no" readonly="1" />
                                    <field name="logistics_status" readonly="1" />
                                    <field name="shipping_carrier" readonly="1" />
                                    <field name="weight" readonly="1" />
                                    <!-- <field name="package_items_ids">
                                        <list>
                                            <field name="item_id" readonly="1" />
                                            <field name="model_id" readonly="1" />
                                            <field name="quantity" readonly="1" />
                                            <field name="order_item_id" readonly="1" />
                                        </list>
                                    </field> -->
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="ecommerce_order_view_pivot" model="ir.ui.view">
        <field name="name">ecommerce.order.view.pivot</field>
        <field name="model">ecommerce.order</field>
        <field name="arch" type="xml">
            <pivot string="Pivot View" disable_linking="true">
                <field name="ecommerce_shop_id" type="row" />
                <field name="order_status" type="row" />
                <field name="order_type" type="row" />
                <field name="utm_medium_id" type="row" />
                <field name="total_amount" type="measure" />
            </pivot>
        </field>
    </record>

    <record id="ecommerce_order_view_graph" model="ir.ui.view">
        <field name="name">ecommerce.order.view.graph</field>
        <field name="model">ecommerce.order</field>
        <field name="arch" type="xml">
            <graph string="Graph View" type="line">
                <field name="ecommerce_shop_id" />
                <field name="total_amount" type="measure" />
                <field name="order_type" />
                <field name="order_status" />
                <field name="utm_medium_id" />
            </graph>
        </field>
    </record>

    <!-- View ecommerce.order search -->
    <record id="view_ecommerce_order_search" model="ir.ui.view">
        <field name="name">view.ecommerce.order.search</field>
        <field name="model">ecommerce.order</field>
        <field name="arch" type="xml">
            <search>
                <searchpanel>
                    <field name="ecommerce_shop_id" icon="fa-shop" string="Shop" select="multi"/>
                    <field name="utm_medium_id" icon="fa-filter" string="UTM Medium" select="multi"/>
                    <field name="order_type" icon="fa-list" string="Order Type" select="multi"/>
                </searchpanel>
                <group expand="1" string="Group By">
                    <filter string="UTM Medium" name="utm_medium_id" domain="[]"
                        context="{'group_by':'utm_medium_id'}" />
                    <filter string="Ecommerce Shop" name="ecommerce_shop_id" domain="[]"
                        context="{'group_by':'ecommerce_shop_id'}" />
                    <filter string="State" name="state" domain="[]"
                        context="{'group_by':'state'}" />
                    <filter string="Order Type" name="order_type" domain="[]"
                        context="{'group_by':'order_type'}" />
                </group>
                <field name="order_code" string="Filter by Order Code"
                    filter_domain="['|',('order_code','ilike',self),('return_order_code','ilike',self)]" />
                <field name="ecommerce_shop_id" string="Filter by Ecommerce Shop" />
                <field name="tracking_code" string="Filter by Tracking Code" />
                <field name='order_line_ids' string="Filter by Product"
                    filter_domain="['|','|','|','|',('order_line_ids.product_name','ilike',self), ('order_line_ids.sku','ilike',self), ('order_line_ids.item_id','ilike',self), ('order_line_ids.model_id','ilike',self), ('order_line_ids.model_name','ilike',self)]" />
            </search>
        </field>
    </record>

    <!-- Action ecommerce.order -->
    <record id="action_ecommerce_order" model="ir.actions.act_window">
        <field name="name">Ecommerce Order</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">ecommerce.order</field>
        <field name="view_mode">list,kanban,form,pivot,graph</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                There is no examples click here to add new Ecommerce Order.
            </p>
        </field>
    </record>

    <menuitem
        id="ecommerce_order_menu"
        name="Ecommerce Order"
        action="action_ecommerce_order"
        parent="ecommerce_menu_root"
        sequence="-1" />
</odoo>