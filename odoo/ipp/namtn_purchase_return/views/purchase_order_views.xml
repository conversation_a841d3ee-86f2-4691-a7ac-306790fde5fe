<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_purchase_order_return_tree" model="ir.ui.view">
        <field name="name">view.purchase.order.return.list</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//list" position="attributes">
                <attribute name="string">Purchase Order Return</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_purchase_order_return_form" model="ir.ui.view">
        <field name="name">view.purchase.order.return.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="string">Purchase Order Return</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="before">
                <field name="order_type" invisible="1" />
            </xpath>
        </field>
    </record>

    <record id="purchase_order_view_form_inherit_inherit_namtn_purchase_return" model="ir.ui.view">
        <field name="name">purchase.order.view.form.inherit.namtn.purchase.return</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase_stock.purchase_order_view_form_inherit" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='picking_type_id']" position="before">
                <field name="picking_type_domain" invisible="1" />
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="domain">picking_type_domain</attribute>
            </xpath>
        </field>
    </record>

    <!-- Action purchase.order -->
    <record id="action_purchase_order_return" model="ir.actions.act_window">
        <field name="name">Purchase Order Return</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('order_type', '=', 'return')]</field>
        <field name="context">{'default_order_type': 'return'}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                There is no examples click here to add new Purchase Order.
            </p>
        </field>
    </record>

    <record id="purchase.purchase_rfq" model="ir.actions.act_window">
        <field name="domain">['|',('order_type', '=', 'purchase'),('order_type', '=', False)]</field>
        <field name="context">{'quotation_only': True, 'default_order_type': 'purchase'}</field>
    </record>

    <record id="purchase.action_rfq_form" model="ir.actions.act_window">
        <field name="context">{'default_order_type': 'purchase'}</field>
    </record>

    <menuitem
        id="purchase_order_return_menu"
        name="Purchase Order Return"
        action="action_purchase_order_return"
        parent="purchase.menu_procurement_management"
        sequence="100" />
</odoo>
