# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    order_type = fields.Selection([
        ('purchase', 'Purchase'),
        ('return', 'Return'),
    ], string='Order Type', default='purchase')

    @api.model
    def _default_picking_type(self):
        return self._get_picking_type(self.env.context.get('company_id') or self.env.company.id)

    @api.model
    def _get_picking_type(self, company_id):
        order_type = self.env.context.get('order_type', 'purchase')
        if order_type == 'return':
            picking_type = self.env['stock.picking.type'].search([('code', '=', 'outgoing'), ('warehouse_id.company_id', '=', company_id)])
            if not picking_type:
                picking_type = self.env['stock.picking.type'].search([('code', '=', 'outgoing'), ('warehouse_id', '=', False)])
                if not picking_type:
                    raise ValidationError(_('Please define outgoing picking type for this company.'))
                return picking_type[:1]
            return picking_type[:1]
        return super()._get_picking_type(company_id)

    picking_type_id = fields.Many2one('stock.picking.type', 'Deliver To', required=True, default=_default_picking_type, domain="['|', ('warehouse_id', '=', False), ('warehouse_id.company_id', '=', company_id)]",
        help="This will determine operation type of incoming shipment", store=True, readonly=False)
    picking_type_domain = fields.Binary("Picking Type Domain", compute='_compute_picking_type_domain')

    @api.depends('picking_type_id', 'partner_id')
    def _compute_dest_address_id(self):
        if self.partner_id:
            self.dest_address_id = self.partner_id
        self.filtered(lambda po: po.picking_type_id.default_location_dest_id.usage != 'customer').dest_address_id = False

    @api.depends('order_type')
    def _compute_picking_type_domain(self):
        for order in self:
            if order.order_type == 'return':
                order.picking_type_domain = [('code', '=', 'outgoing')]
            elif order.order_type == 'purchase':
                order.picking_type_domain = [('code', '=', 'incoming')]

    def _prepare_picking(self):
        res = super()._prepare_picking()
        if self.order_type == 'return':
            res['location_id'] = self.picking_type_id.default_location_src_id.id
        return res
